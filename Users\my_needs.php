<?php
session_start();
if(isset($_SESSION['user'])){
    if($_SESSION['user']->role==="User"){
        
    }else{
        header("location:../login.php",true);
        die("");
    }
}else{
    header("location:../login.php",true);
    die("");
}

include "addon/dbcon.php";

$user_id = $_SESSION['user']->id_user;
$query = "SELECT nr.*, u.user_name as response_by_name 
          FROM needs_requests nr 
          LEFT JOIN users_tb u ON nr.response_by = u.id_user 
          WHERE nr.user_id = ? 
          ORDER BY nr.created_at DESC";
$stmt = $con->prepare($query);
$stmt->bind_param("i", $user_id);
$stmt->execute();
$result = $stmt->get_result();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>احتياجاتي</title>
    <link rel="stylesheet" href="css/bootstrap.min.css">
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/all.min.css">
    <link rel="icon" href="css/icon.ico">
    <?php include "addon/topbar.php" ?>
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Cairo', sans-serif;
        }
        
        .needs-container {
            max-width: 1200px;
            margin: 50px auto;
            padding: 20px;
        }
        
        .page-header {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            text-align: center;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        
        .page-title {
            color: #2c3e50;
            font-size: 28px;
            font-weight: bold;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .need-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }
        
        .need-card:hover {
            transform: translateY(-5px);
        }
        
        .need-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            flex-wrap: wrap;
        }
        
        .need-title {
            font-size: 20px;
            font-weight: bold;
            color: #2c3e50;
        }
        
        .status-badge {
            padding: 8px 15px;
            border-radius: 20px;
            font-weight: bold;
            font-size: 14px;
        }
        
        .status-pending {
            background: linear-gradient(45deg, #ffc107, #fd7e14);
            color: white;
        }
        
        .status-approved {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
        }
        
        .status-rejected {
            background: linear-gradient(45deg, #dc3545, #fd7e14);
            color: white;
        }
        
        .need-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 15px;
        }
        
        .info-item {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #495057;
        }
        
        .info-item i {
            color: #667eea;
            width: 20px;
        }
        
        .need-details {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 15px;
            border-right: 4px solid #667eea;
        }
        
        .admin-response {
            background: linear-gradient(45deg, #e3f2fd, #f3e5f5);
            padding: 15px;
            border-radius: 10px;
            border-right: 4px solid #667eea;
        }
        
        .response-header {
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        }
        
        .new-request-btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 15px 30px;
            border-radius: 50px;
            text-decoration: none;
            font-weight: bold;
            display: inline-block;
            margin-bottom: 20px;
            transition: all 0.3s ease;
        }
        
        .new-request-btn:hover {
            background: linear-gradient(45deg, #5a6fd8, #6a4190);
            transform: translateY(-2px);
            color: white;
            text-decoration: none;
        }
        
        .empty-state {
            text-align: center;
            padding: 50px;
            color: #6c757d;
        }
        
        .empty-state i {
            font-size: 64px;
            margin-bottom: 20px;
            color: #dee2e6;
        }
    </style>
</head>
<body>
    <div class="needs-container">
        <div class="page-header">
            <h1 class="page-title">
                <i class="fas fa-clipboard-list"></i>
                احتياجاتي
            </h1>
            <a href="request_need.php" class="new-request-btn">
                <i class="fas fa-plus"></i>
                طلب احتياج جديد
            </a>
        </div>
        
        <?php if($result->num_rows > 0): ?>
            <?php while($need = $result->fetch_assoc()): ?>
                <div class="need-card">
                    <div class="need-header">
                        <h3 class="need-title"><?php echo htmlspecialchars($need['need_name']); ?></h3>
                        <span class="status-badge status-<?php 
                            echo $need['status'] == 'قيد المراجعة' ? 'pending' : 
                                ($need['status'] == 'تم التوفير' ? 'approved' : 'rejected'); 
                        ?>">
                            <?php echo $need['status']; ?>
                        </span>
                    </div>
                    
                    <div class="need-info">
                        <div class="info-item">
                            <i class="fas fa-calendar"></i>
                            <span>تاريخ الطلب: <?php echo date('Y/m/d', strtotime($need['request_date'])); ?></span>
                        </div>
                        <div class="info-item">
                            <i class="fas fa-tag"></i>
                            <span>النوع: <?php echo $need['need_type']; ?></span>
                        </div>
                        <?php if($need['response_date']): ?>
                            <div class="info-item">
                                <i class="fas fa-clock"></i>
                                <span>تاريخ الرد: <?php echo date('Y/m/d H:i', strtotime($need['response_date'])); ?></span>
                            </div>
                        <?php endif; ?>
                    </div>
                    
                    <div class="need-details">
                        <strong>تفاصيل الاحتياج:</strong><br>
                        <?php echo nl2br(htmlspecialchars($need['need_details'])); ?>
                    </div>
                    
                    <?php if($need['admin_response']): ?>
                        <div class="admin-response">
                            <div class="response-header">
                                <i class="fas fa-reply"></i>
                                رد الإدارة:
                                <?php if($need['response_by_name']): ?>
                                    (بواسطة: <?php echo htmlspecialchars($need['response_by_name']); ?>)
                                <?php endif; ?>
                            </div>
                            <?php echo nl2br(htmlspecialchars($need['admin_response'])); ?>
                        </div>
                    <?php endif; ?>
                </div>
            <?php endwhile; ?>
        <?php else: ?>
            <div class="need-card">
                <div class="empty-state">
                    <i class="fas fa-clipboard-list"></i>
                    <h3>لا توجد طلبات احتياج</h3>
                    <p>لم تقم بإرسال أي طلبات احتياج بعد</p>
                    <a href="request_need.php" class="new-request-btn">
                        <i class="fas fa-plus"></i>
                        إرسال طلب احتياج
                    </a>
                </div>
            </div>
        <?php endif; ?>
    </div>
</body>
</html>
