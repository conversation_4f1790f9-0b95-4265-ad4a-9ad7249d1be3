<?php
session_start();
if(isset($_SESSION['user'])){
    if($_SESSION['user']->role==="Admin"){
        

 }else{
        header("location:../login.php",true);
        die("");
        echo "dont work";
    }
    }else{
        header("location:../login.php",true);
        die("");
}

?>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>معلومات الطلاب</title>
    <link rel="stylesheet" href="css/bootstrap.min.css">
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/all.min.css">
    <script src="js/all.min.js"></script>
    <link rel="icon" href="css/icon.ico">
    <script src="js/jquery.min.js"></script>
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
  
  
  <link rel="stylesheet" href="css/bootstrap.min.css">
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/all.min.css">
    <script src="js/all.min.js"></script>
    <link rel="icon" href="css/icon.ico">
    <script src="js/jquery.min.js"></script>
    <script src="js/jquery.dataTables.min.js"></script>
    <link rel="stylesheet" href="css/jquery.dataTables.min.css">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <style>
        .statistics-container {
            margin: 20px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
        }

        .card-header {
            border-bottom: none;
            font-weight: bold;
            font-size: 14px;
        }

        .card-title {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 0;
        }

        .card-text {
            font-size: 0.9rem;
            opacity: 0.9;
        }

        .bg-success {
            background: linear-gradient(135deg, #28a745, #20c997) !important;
        }

        .bg-warning {
            background: linear-gradient(135deg, #ffc107, #fd7e14) !important;
        }

        .bg-danger {
            background: linear-gradient(135deg, #dc3545, #e83e8c) !important;
        }

        .bg-info {
            background: linear-gradient(135deg, #17a2b8, #6f42c1) !important;
        }

        .bg-primary {
            background: linear-gradient(135deg, #007bff, #6610f2) !important;
        }
    </style>
  
  
  
  
      <?php include "addon/topbar.php" ?>
      <?php include "addon/dbcon.php" ?>
   </head>
    <body>
    <form action="" metho="POST">
       <div class="search">
      <select name="users" id="selc" placeholder='اختر مستخدم'>
        <option value="0" selected >اختر احد المستخدمين</option>
            <?php
            $sql="SELECT * FROM users_tb ";
            $result=mysqli_query($con,$sql);
            if($result){
                while($row=mysqli_fetch_assoc($result)){
                    $user_name=$row['user_name'];
                    $id=$row['id_user'];
                echo '<option value='.$id.'>'.$user_name.'</option>';
                }
            }
            ?>   
        </select>
  <button class="btn btn-success text-light" name="sub"  ><a href="../Admin/addon/exportStud.php">تحميل اكسل</a> </button>
  <button class="btn btn-primary text-light" onclick="printStudentsTable()">طباعة</button>
    <button class="btn btn-secondary text-light" name="sub"  ><a href="search.php">بحث</a> </button>
    
    
  </div>
  </form>
  <!-- بطاقات الإحصائيات -->
  <div class="statistics-container" style="display: none; margin: 20px 0;" id="statisticsContainer">
      <div class="row">
          <!-- بطاقة الطلاب الفعالين -->
          <div class="col-md-3 mb-3">
              <div class="card text-white bg-success">
                  <div class="card-header">
                      <i class="fas fa-user-check"></i> الطلاب الفعالين
                  </div>
                  <div class="card-body">
                      <h4 class="card-title" id="activeStudents">0</h4>
                      <p class="card-text">طالب فعال</p>
                  </div>
              </div>
          </div>

          <!-- بطاقة قريب الانتهاء -->
          <div class="col-md-3 mb-3">
              <div class="card text-white bg-warning">
                  <div class="card-header">
                      <i class="fas fa-clock"></i> قريب الانتهاء
                  </div>
                  <div class="card-body">
                      <h4 class="card-title" id="soonExpiring">0</h4>
                      <p class="card-text">خلال 10 أيام</p>
                  </div>
              </div>
          </div>

          <!-- بطاقة المنتهي الاشتراك -->
          <div class="col-md-3 mb-3">
              <div class="card text-white bg-danger">
                  <div class="card-header">
                      <i class="fas fa-user-times"></i> منتهي الاشتراك
                  </div>
                  <div class="card-body">
                      <h4 class="card-title" id="expiredStudents">0</h4>
                      <p class="card-text">طالب منتهي</p>
                  </div>
              </div>
          </div>

          <!-- بطاقة التسجيل الجديد -->
          <div class="col-md-3 mb-3">
              <div class="card text-white bg-info">
                  <div class="card-header">
                      <i class="fas fa-user-plus"></i> تسجيل جديد
                  </div>
                  <div class="card-body">
                      <h4 class="card-title" id="newStudents">0</h4>
                      <p class="card-text">هذا الشهر</p>
                  </div>
              </div>
          </div>
      </div>

      <!-- بطاقة المجموع الكلي -->
      <div class="row">
          <div class="col-md-12 mb-3">
              <div class="card text-white bg-primary">
                  <div class="card-header">
                      <i class="fas fa-users"></i> المجموع الكلي
                  </div>
                  <div class="card-body">
                      <h4 class="card-title" id="totalStudents">0</h4>
                      <p class="card-text">إجمالي عدد الطلاب</p>
                  </div>
              </div>
          </div>
      </div>
  </div>
  


    <table class="table" id="Table">
  <thead>
    <tr>
    <th scope="col">  العمليات  </th>
    <th scope="col"> مستخدم الحضانة  </th>
    <th scope="col" > الايام المتبقية </th>
      <th scope="col">حالة الاشتراك</th>
      <th scope="col">قيمة الاشتراك</th>
      <th scope="col">تاريخ النفاذ </th>
      <th scope="col" >تاريخ الاشتراك</th>
      <th scope="col">رقم ولي الامر</th>
      <th scope="col">صنف التسجيل</th>
    <th scope="col">السكن</th> 
      <th scope="col">الجنس</th>
      <th scope="col"> العمر</th>
       <th scope="col">التسجيل</th>
      <th scope="col" >اسم الطالب  </th>
      <th scope="col">رقم  الوصل </th>
      
    </tr>
  </thead>
  <tbody id="myTable">
  
   
  </tbody>
</table>
<section id="deletmodle">
      <div class="modal-box">
        <h2>! تنبيه </h2>
        <h3>هل انته متأكد من عملية الحذف كون البيانات لايمكن استرجاعها</h3>

        <div class="buttons-method">
          <button class="close-btn">اغلاق</button>
         <button name="remov" id="rm" class="remove-btn">حذف</button>
        </div>
      </div>
    </section>
    <script>
      function deletdata(id){
        $("#deletmodle").addClass("active");
        $(".table").addClass("active");
        $(".close-btn").click(function () { 
          $(".table").removeClass("active")
          $("#deletmodle").removeClass("active")
        });
        $("#rm").click(function () { 
          $(".table").removeClass("active")
          $.ajax({url:'addon/reomves.php',
          method:"POST",
          data:({removeid:id}),
          success:function(response){
          $("#deletmodle").removeClass("active");
          jQuery("#tr_"+id).css("background","#eaeaea");
          jQuery("#tr_"+id).hide(2000);
          setInterval(function () {
            window.location.reload();
           },2000);
         
        }
        });
        });
        }
    </script>
  
    <script>
      // وظيفة لتحديث الإحصائيات
      function updateStatistics(stats) {
          // إظهار حاوية الإحصائيات
          document.getElementById('statisticsContainer').style.display = 'block';

          // تحديث القيم
          document.getElementById('activeStudents').textContent = stats.active || 0;
          document.getElementById('soonExpiring').textContent = stats.soon || 0;
          document.getElementById('expiredStudents').textContent = stats.expired || 0;
          document.getElementById('newStudents').textContent = stats.newReg || 0;
          document.getElementById('totalStudents').textContent = stats.total || 0;
      }

      // وظيفة لإخفاء الإحصائيات
      function hideStatistics() {
          document.getElementById('statisticsContainer').style.display = 'none';
      }
    </script>
  
   </body>
   
   <script>
 
    $("#selc").change(function(){
      var selc =$(this).val();
      console.log(selc)

      if(selc == 0) {
          // إخفاء الإحصائيات إذا لم يتم اختيار مستخدم
          hideStatistics();
          $("#myTable").html("");
          return;
      }

        // تحميل البيانات والإحصائيات
        $.ajax({
          method: "POST",
          url: "addon/infostudF.php",
          data: {id:selc},
          success: function (data) {
            // مسح الجدول القديم أولاً
            if ($.fn.DataTable.isDataTable('#Table')) {
                $('#Table').DataTable().destroy();
            }

            // إضافة البيانات الجديدة
            $("#myTable").html(data);

            // إعادة تهيئة DataTable
            $("#Table").DataTable();
          },
          error: function(xhr, status, error) {
              console.log("Error: " + error);
              console.log("Response: " + xhr.responseText);
              alert("حدث خطأ في تحميل البيانات: " + error);
          }
        })
      });
;
</script>

<script>
function printStudentsTable(){
    // إنشاء نافذة جديدة للطباعة
    var printWindow = window.open('', '_blank');

    // نسخ الجدول وإزالة عمود العمليات
    var table = document.getElementById('Table').cloneNode(true);

    // إزالة عمود العمليات من الرأس والصفوف
    var rows = table.querySelectorAll('tr');
    rows.forEach(function(row) {
        var cells = row.querySelectorAll('th, td');
        // إزالة العمود الأول (العمليات) إذا وجد
        if (cells.length > 0 && (cells[0].textContent.includes('العمليات') || cells[0].querySelector('button') || cells[0].querySelector('a'))) {
            cells[0].remove();
        }
    });

    var tableContent = table.outerHTML;

    // إنشاء محتوى HTML للطباعة
    var printContent = `
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
        <meta charset="UTF-8">
        <title>تقرير معلومات الطلاب</title>
        <style>
            @font-face {
                font-family:"LamaSans-Medium";
                src: url(css/JannaLT.ttf);
            }

            body {
                font-family: "LamaSans-Medium", Arial, sans-serif;
                direction: rtl;
                margin: 20px;
                background: white;
            }

            .print-header {
                text-align: center;
                margin-bottom: 30px;
                border-bottom: 2px solid #333;
                padding-bottom: 20px;
            }

            .print-header h1 {
                color: #333;
                margin-bottom: 10px;
                font-size: 24px;
            }

            .print-header p {
                color: #666;
                margin: 5px 0;
                font-size: 14px;
            }

            table {
                width: 100%;
                border-collapse: collapse;
                margin: 20px 0;
                font-size: 12px;
            }

            table th, table td {
                border: 1px solid #333;
                padding: 8px;
                text-align: center;
            }

            table thead {
                background-color: #f8f9fa;
                font-weight: bold;
            }

            table thead th {
                background-color: #333;
                color: white;
                font-weight: bold;
            }

            .still {
                color: green;
                font-weight: bold;
            }

            .exp {
                color: red;
                font-weight: bold;
            }

            .soon {
                color: orange;
                font-weight: bold;
            }

            .print-footer {
                margin-top: 30px;
                text-align: center;
                font-size: 12px;
                color: #666;
                border-top: 1px solid #ccc;
                padding-top: 10px;
            }

            @media print {
                body { margin: 0; }
                .print-header { page-break-inside: avoid; }
                table { page-break-inside: auto; }
                tr { page-break-inside: avoid; page-break-after: auto; }
            }
        </style>
    </head>
    <body>
        <div class="print-header">
            <h1>تقرير معلومات الطلاب</h1>
            <p>تاريخ التقرير: ${new Date().toLocaleDateString('ar-EG')}</p>
            <p>وقت الطباعة: ${new Date().toLocaleTimeString('ar-EG')}</p>
        </div>

        ${tableContent}

        <div class="print-footer">
            <p>تم إنشاء هذا التقرير بواسطة نظام إدارة الروضة</p>
        </div>
    </body>
    </html>
    `;

    // كتابة المحتوى في النافذة الجديدة
    printWindow.document.write(printContent);
    printWindow.document.close();

    // انتظار تحميل المحتوى ثم طباعة
    printWindow.onload = function() {
        printWindow.print();
        printWindow.close();
    };
}
</script>
</html>