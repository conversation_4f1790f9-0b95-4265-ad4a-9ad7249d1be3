<?php
if(isset($_SESSION['user'])){
    if($_SESSION['user']->role==="User"){
        

 }else{
        header("location:../login.php",true);
        die("");
       
    }
    }else{
        header("location:../login.php",true);
        die("");
      }
      if(isset($_GET['exitbtn'])){
        header("location:../logout.php",true);
        exit();
      }
    

?>


<nav>
<div class='logo2'><img src="../Admin/css/logooo.png" alt=""></div>

         <!-- الأزرار الجديدة -->
         <div class="quick-actions">
             <a href="../Users/<USER>" class="quick-btn" title="طلب احتياج">
                 <i class="fa-solid fa-clipboard-list"></i>
                 <span>طلب احتياج</span>
             </a>
             <a href="../Users/<USER>" class="quick-btn" title="احتياجاتي">
                 <i class="fa-solid fa-eye"></i>
                 <span>احتياجاتي</span>
             </a>
             <a href="../Users/<USER>" class="quick-btn" title="طلب إجازة">
                 <i class="fa-solid fa-calendar-alt"></i>
                 <span>طلب إجازة</span>
             </a>
             <a href="../Users/<USER>" class="quick-btn" title="إجازاتي">
                 <i class="fa-solid fa-calendar-check"></i>
                 <span>إجازاتي</span>
             </a>
         </div>

         <div class="logo">

         <form action=""><button class='btn btn-danger mb-1' name="exitbtn" type='submit' id='exit_btn'>تسجيل الخروج </button>
         <label for="" ><?php echo $_SESSION['user']->user_name; ?> مرحبا بك</label></form>

         </div>
         <input type="checkbox" id="click">
         <label for="click" class="menu-btn" id="caps">
         <i id="capss" class="fa-solid fa-bars " ></i>
         </label>
         
         <ul>
            <li><a href="../Users/<USER>"><i class="fa-solid fa-exclamation"></i> ماذا عنا</a></li>
            <li><a href="../Users/<USER>"><i class="fa-solid fa-user-group"></i> الموظفين</a></li>
            <li><a href="../Users/<USER>"> <i class="fa-solid fa-money-bill-transfer"></i> المصاريف </a></li>
            <li><a href="../Users/<USER>"><i class="fa-solid fa-school"></i> حضور الطلاب </a></li>
            <li><a href="../Users/<USER>"><i class="fa-solid fa-school"></i> حضور الموظفين </a></li>
            <li><a href="../Users/<USER>"><i class="fa-solid fa-graduation-cap"></i> الطلاب </a></li>
            <li><a href="../Users/<USER>"><i class="fa-solid fa-user-plus"></i> اضافة طالب</a></li>
            <li><a class="active" href="../Users/<USER>">الرئيسية</a></li>


         </ul>
      </nav>
