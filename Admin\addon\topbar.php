<?php
if(isset($_SESSION['user'])){
    if($_SESSION['user']->role==="Admin"){
        

 }else{
        header("location:../login.php",true);
        die("");

    }
    }else{
        header("location:../login.php",true);
        die("");
      }
      if(isset($_GET['exitbtn'])){
        header("location:../logout.php",true);
        exit();
      }
    

?>

<nav>
   <style>
      #capss{
         
         color: aliceblue;
         font-size: 30px;
         cursor: pointer;
      }
      #capss:hover{
         color: gray;
      }
   </style>
<div class='logo2'><img src="../Admin/css/logooo.png" alt=""></div>
         <div class="logo">
         
         <form action=""><button class='btn btn-danger mb-1' name="exitbtn" type='submit' id='exit_btn'>تسجيل الخروج </button>
         <label for="" ><?php echo $_SESSION['user']->user_name; ?> مرحبا بك</label></form>
         
         </div>
         <input type="checkbox" id="click">
         <label for="click" class="menu-btn" id="caps">
         <i id="capss" class="fa-solid fa-bars" ></i>
         </label>
         
         <ul>
            <li><a href="../Admin/about_us.php"><i class="fa-solid fa-exclamation"></i> ماذا عنا</a></li>
            <li><a href="../Admin/addon/bkF.php"><i class="fa-solid fa-server"></i> BackUP </a></li>
            <!-- <li class="repo"><a href='#'><i class="fa-solid fa-caret-down"></i><i class="fa-solid fa-file-contract"></i>التقارير </a> -->
             <!--  <ul class="down_menu"> -->
                  <li><a href="../Admin/attandec.php" style="color:#FFF5CD">التقارير - حضور الطلاب</a></li>
                  <li><a href="../Admin/reports.php" style="color:#FFF5CD">التقرير - الطلاب الفعالين</a></li>
           <!--    </ul> -->
         <!--  </li> -->
            <li><a href="../Admin/info_employ.php"><i class="fa-solid fa-user-group"></i> الموظفين</a></li>
            <li><a href="../Admin/info_user.php"><i class="fa-solid fa-users"></i> المستخدمين</a></li>
            <li><a href="../Admin/acounting.php" ><i class="fa-solid fa-file-signature"></i> الحسابات</a></li>
            <li><a href="../Admin/info_depit.php"> <i class="fa-solid fa-money-bill-transfer"></i> المصاريف </a></li>
            <li><a href="../Admin/infost.php"><i class="fa-solid fa-graduation-cap"></i> الطلاب </a></li>
            <li><a href="../Admin/addstud.php"><i class="fa-solid fa-user-plus"></i> اضافة طالب</a></li>
            <li><a class="active" href="../Admin/home.php">الرئيسية</a></li>
            
            
         </ul>
      </nav>
