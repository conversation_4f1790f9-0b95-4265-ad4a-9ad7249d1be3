<?php
session_start();
if(isset($_SESSION['user'])){
    if($_SESSION['user']->role==="Admin"){
        

 }else{
        header("location:../login.php",true);
        die("");
        echo "dont work";
    }
    }else{
        header("location:../login.php",true);
        die("");
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الحاسابات حسب التاريخ</title>
    <link rel="stylesheet" href="css/bootstrap.min.css">
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/all.min.css">
    <script src="js/all.min.js"></script>
    <link rel="icon" href="css/icon.ico">
    <style>
        .sortable-header {
            cursor: pointer;
            position: relative;
            user-select: none;
        }
        .sort-arrow {
            margin-left: 5px;
            font-size: 12px;
            color: #6c757d;
        }
        .sort-arrow.active {
            color: #007bff;
        }
    </style>
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <?php include "addon/topbar.php" ?>
      <?php include "addon/dbcon.php" ?>

   </head>
   <body>
   <form action="" method="POST">
  <div class="search_ac">
     <select name="acout_type" id="selc2" placeholder='اختر مستخدم' required>
      <option value="0" >اختر مستخدم </option>
     <?php
            $sql="SELECT * FROM users_tb ";
            $result=mysqli_query($con,$sql);
            if($result){
                while($row=mysqli_fetch_assoc($result)){
                    $user_name=$row['user_name'];
                    $id=$row['id_user'];
                echo '<option value='.$id.'>'.$user_name.'</option>';
                }
            }
            ?>
     
     </select>
    <input name="datee" type="date" required>
    <label for=""> الى تاريخ</label>
    <input name="dates" type="date" required>
    <label for="">اختر من تاريخ</label>
    <button class="btn btn-warning" name='myInput'> بحث</button>
    
  </div>
 

      </div>
      <div class="wrapper2" id="tost_info">
        <div id="toast2">
            <div class="container-11">
            <i id="icon" class="fa-solid fa-circle-info"></i>
            </div>
            <div class="container-22">
                <p id="titlecash">مجموع الايرادات  </p>
                <p id="text">تم اضافة البينانات</p>
            </div>
        </div>
    </div>
    <div class="wrapper3" id="tost_info">
        <div id="toast3">
            <div class="container-33">
            <i id="icon3" class="fa-solid fa-circle-info"></i>
            </div>
            <div class="container-33">
                <p id="titlecash3">مجموع المصروفات</p>
                <p id="text3">تم اضافة البينانات</p>
            </div>
        </div>
    </div>
    <div class="wrapper5" id="tost_info">
        <div id="toast5">
            <div class="container-55">
            <i id="icon5" class="fa-solid fa-circle-info"></i>
            </div>
            <div class="container-55">
                <p id="titlecash5"></p>
                <p id="text5">  مجموع الرواتب </p>
            </div>
        </div>
    </div>
    <div class="wrapper4" id="tost_info">
        <div id="toast4">
            <div class="container-44">
            <i id="icon4" class="fa-solid fa-circle-info"></i>
            </div>
            <div class="container-44">
                <p id="titlecash4">صافي الربح او الخسارة</p>
                <p id="text4">تم اضافة البينانات</p>
            </div>
        </div>
    </div>
    <script >
let x;
let toast = document.getElementById("toast2"),
    text=document.getElementById("text"),
    titlecash=document.getElementById("titlecash"),
    icon=document.getElementById("icon")
function Toastincoming(ss){
  cash=new Intl.NumberFormat('de-DE', { style: 'currency' ,currency:"IQD" }).format(ss);
    clearTimeout(x);
    titlecash.innerText="مجموع الايرادات "
    icon.className="fa-solid fa-money-bills";
    text.style.fontSize="18px"
    text.innerText=cash;
    toast.style.transition='1s';
    toast.style.transform = "translateX(0)";
    toast.style.transition='1s';
    x = setTimeout(()=>{
        toast.style.transform = "translateX(-500px)"
    }, 500000);
}
</script>
<script >
let y;
let toast3 = document.getElementById("toast3"),
    text3=document.getElementById("text3"),
    titlecash3=document.getElementById("titlecash3"),
    icon3=document.getElementById("icon3");

function ToastDepit(bb){
    cash2=new Intl.NumberFormat('de-DE', { style: 'currency' ,currency:"IQD" }).format(bb);
    clearTimeout(y);
    toast3.style.borderRight="8px solid rgb(255 9 9)";
    icon3.style.color="red"
    icon3.className="fa-solid fa-money-bill-1-wave";
    text3.style.fontSize="18px"
    text3.innerText=cash2;
    toast3.style.transition='1s';
    toast3.style.transform = "translateX(0)";
    toast3.style.transition='1s';
    y=setTimeout(()=>{
        toast3.style.transform = "translateX(-500px)"
    }, 600000)   
}

</script>
<script >
let c;
let toast5 = document.getElementById("toast5"),
    text5=document.getElementById("text5"),
    titlecash5=document.getElementById("titlecash5"),
    icon5=document.getElementById("icon5");
function tostemploye(xx){
    cash5=new Intl.NumberFormat('de-DE', { style: 'currency' ,currency:"IQD" }).format(xx);
    clearTimeout(c);
    titlecash5.innerText="مجموع الرواتب "
    toast5.style.borderRight="8px solid rgb(178, 102, 255)";
    icon5.style.color="rgb(178, 102, 255)"
    icon5.className="fa-solid fa-people-group";
    text5.style.fontSize="18px"
    text5.innerText=cash5;
    toast5.style.transition='1s';
    toast5.style.transform = "translateX(0)";
    toast5.style.transition='1s';
    c=setTimeout(()=>{
        toast5.style.transform = "translateX(-500px)"
    }, 650000)   
}

</script>
<script >
let z;
let toast4 = document.getElementById("toast4"),
    text4=document.getElementById("text4"),
    titlecash4=document.getElementById("titlecash4"),
    icon4=document.getElementById("icon4");
function prof(cc){
    cash3=new Intl.NumberFormat('de-DE', { style: 'currency' ,currency:"IQD" }).format(cc);
    clearTimeout(z);
    toast4.style.borderRight="8px solid rgb(0 194 255)";
    icon4.style.color="#00c2ff"
    icon4.className="fa-solid fa-sack-dollar";
    text4.style.fontSize="18px"
    text4.innerText=cash3;
    toast4.style.transition='1s';
    toast4.style.transform = "translateX(0)";
    toast4.style.transition='1s';
    z = setTimeout(()=>{
        toast4.style.transform = "translateX(-500px)"
    }, 700000);

}

</script>


    <?php
    if(isset($_POST['myInput']))
    { 
      if($_POST['acout_type']){
      $id=$_POST['acout_type'];
      $dates=$_POST['dates'];
      $datee=$_POST['datee'];
      $query=$query="SELECT * FROM users_tb,stud_tb,stud_pay WHERE users_tb.id_user=$id AND stud_pay.id_stud=stud_tb.id AND stud_tb.userID=users_tb.id_user AND DATE(stud_tb.datein) BETWEEN '$dates' AND '$datee'";
      $query_run=mysqli_query($con,$query);

        ?>

          <table class="table" id="studentsTable">
        <thead>
          <tr>
            <th scope="col" class="sortable-header" onclick="sortTable('studentsTable', 0)">
              مستخدم الحضانة
              <span class="sort-arrow"><i class="fas fa-sort"></i></span>
            </th>
            <th scope="col" class="sortable-header" onclick="sortTable('studentsTable', 1)">
              تاريخ التسجيل
              <span class="sort-arrow"><i class="fas fa-sort"></i></span>
            </th>
            <th scope="col" class="sortable-header" onclick="sortTable('studentsTable', 2)">
              قيمة الاشتراك
              <span class="sort-arrow"><i class="fas fa-sort"></i></span>
            </th>
            <th scope="col" class="sortable-header" onclick="sortTable('studentsTable', 3)">
              اسم الطالب
              <span class="sort-arrow"><i class="fas fa-sort"></i></span>
            </th>
            <th scope="col" class="sortable-header" onclick="sortTable('studentsTable', 4)">
              رقم الوصل
              <span class="sort-arrow"><i class="fas fa-sort"></i></span>
            </th>
          </tr>
        </thead>
        <tbody>
          <?php
          $count=0;
         foreach($query_run as $items){
          $count+=$items['cash_stud'];
        

          ?>
          <tr>
          <td><?= $items['user_name'];?></td>
          <td><?= $items['datein'];?></td>
          <td><?=number_format($items['cash_stud']);?></td>
          <td><?= $items['name'];?></td>
          <td><?= $items['id_note'];?></td>
          </tr>
 
 
        <?php



      }
        ?>
        </tbody>
        </table>
        <?php
      echo " <script> Toastincoming(".$count.") </script>";
      }
     
    }
 
        if(isset($_POST['myInput'])){
          if($_POST['acout_type']){
            $id=$_POST['acout_type'];
          $dates=$_POST['dates'];
          $datee=$_POST['datee'];
        $query2="SELECT * FROM users_tb,depit_tb WHERE users_tb.id_user=$id AND depit_tb.userID=users_tb.id_user AND DATE(depit_tb.depit_date) BETWEEN '$dates' AND '$datee'";
        $query_run2=mysqli_query($con,$query2);
        $count2=0;
        ?>
        <table class="table" id="expensesTable">
        <thead>
          <tr>
            <th scope="col" class="sortable-header" onclick="sortTable('expensesTable', 0)">
              مستخدم الحضانة
              <span class="sort-arrow"><i class="fas fa-sort"></i></span>
            </th>
            <th scope="col" class="sortable-header" onclick="sortTable('expensesTable', 1)">
              التاريخ الاسبوعي
              <span class="sort-arrow"><i class="fas fa-sort"></i></span>
            </th>
            <th scope="col" class="sortable-header" onclick="sortTable('expensesTable', 2)">
              قيمة المصروفات
              <span class="sort-arrow"><i class="fas fa-sort"></i></span>
            </th>
            <th scope="col" class="sortable-header" onclick="sortTable('expensesTable', 3)">
              وصف المصروفات
              <span class="sort-arrow"><i class="fas fa-sort"></i></span>
            </th>
          </tr>
        </thead>
        <tbody>
     
        <?php
        foreach($query_run2 as $items2){
          
          $count2+=$items2['depit_cash'];
          ?>
          <tr>
           <td><?= $items2['user_name'];?></td>
           <td><?= $items2['depit_date'];?></td>
           <td><?= number_format($items2['depit_cash']);?></td>
           <td><?= $items2['depit_note'];?></td>
           </tr>
 
 
        <?php
 

        }
        ?>
        </tbody>
        </table>
        <?php
        $final=$count-$count2;
        echo " <script>ToastDepit(".$count2.")</script>";

        }
        if(isset($_POST['myInput'])){
          if($_POST['acout_type']){
            $id=$_POST['acout_type'];
        $query3="SELECT * FROM employ_tb,users_tb WHERE users_tb.id_user=employ_tb.userID AND users_tb.id_user=$id";
        $query_run=mysqli_query($con,$query3);
        $count3=0;
        ?>
        <table class="table" id="employeesTable">
        <thead>
          <tr>
            <th scope="col" class="sortable-header" onclick="sortTable('employeesTable', 0)">
              مستخدم الحضانة
              <span class="sort-arrow"><i class="fas fa-sort"></i></span>
            </th>
            <th scope="col" class="sortable-header" onclick="sortTable('employeesTable', 1)">
              الراتب الشهري
              <span class="sort-arrow"><i class="fas fa-sort"></i></span>
            </th>
            <th scope="col" class="sortable-header" onclick="sortTable('employeesTable', 2)">
              المسمى الوظيفي
              <span class="sort-arrow"><i class="fas fa-sort"></i></span>
            </th>
            <th scope="col" class="sortable-header" onclick="sortTable('employeesTable', 3)">
              تاريخ المباشرة
              <span class="sort-arrow"><i class="fas fa-sort"></i></span>
            </th>
            <th scope="col" class="sortable-header" onclick="sortTable('employeesTable', 4)">
              اسم الموظف
              <span class="sort-arrow"><i class="fas fa-sort"></i></span>
            </th>
          </tr>
        </thead>
        <tbody>
     
        <?php
        foreach($query_run as $items){
            $salary= $items['salary'];
            $count3=$count3+$salary;
            ?>
            <tr>
            <td><?= $items['user_name']; ?></td>
            <td> IQD <?= number_format($count3); ?></td>
            <td><?= $items['job']; ?></td>
            <td><?= $items['date_start']; ?></td>
            <td><?= $items['f_name']; ?></td>
            </tr>
           <?php

        }
        ?>
        </tbody>
        </table>
        <?php

      }

      }
      $final=$count-$count2;
      $final=$final-$count3;
      echo"<script>tostemploye(".$count3.")</script>";
      echo " <script>prof(".$final.")</script>";
    }   
      
    
    ?>



   <script>
   function sortTable(tableId, columnIndex) {
       const table = document.getElementById(tableId);
       if (!table) return;

       const tbody = table.querySelector('tbody');
       if (!tbody) return;

       const rows = Array.from(tbody.querySelectorAll('tr'));
       if (rows.length === 0) return;

       const headers = table.querySelectorAll('th');
       if (columnIndex >= headers.length) return;

       const header = headers[columnIndex];
       const sortArrow = header.querySelector('.sort-arrow i');
       if (!sortArrow) return;

       // Reset all other arrows in this table
       table.querySelectorAll('.sort-arrow i').forEach(arrow => {
           if (arrow !== sortArrow) {
               arrow.className = 'fas fa-sort';
               arrow.parentElement.classList.remove('active');
           }
       });

       let isAscending = true;

       // Check current sort direction
       if (sortArrow.classList.contains('fa-sort-up')) {
           isAscending = false;
           sortArrow.className = 'fas fa-sort-down';
       } else {
           isAscending = true;
           sortArrow.className = 'fas fa-sort-up';
       }

       sortArrow.parentElement.classList.add('active');

       // Sort rows
       rows.sort((a, b) => {
           if (!a.cells[columnIndex] || !b.cells[columnIndex]) {
               return 0;
           }

           const aText = a.cells[columnIndex].textContent.trim();
           const bText = b.cells[columnIndex].textContent.trim();

           // Check if it's a number (for amounts and IDs)
           const aNum = parseFloat(aText.replace(/[^\d.-]/g, ''));
           const bNum = parseFloat(bText.replace(/[^\d.-]/g, ''));

           let comparison = 0;

           if (!isNaN(aNum) && !isNaN(bNum) && aText.match(/\d/)) {
               // Numeric comparison
               comparison = aNum - bNum;
           } else {
               // Text comparison
               comparison = aText.localeCompare(bText, 'ar', {numeric: true});
           }

           return isAscending ? comparison : -comparison;
       });

       // Clear tbody and append sorted rows
       tbody.innerHTML = '';
       rows.forEach(row => tbody.appendChild(row));
   }

   </script>

   </body>
</html>