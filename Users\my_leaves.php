<?php
session_start();
if(isset($_SESSION['user'])){
    if($_SESSION['user']->role==="User"){
        
    }else{
        header("location:../login.php",true);
        die("");
    }
}else{
    header("location:../login.php",true);
    die("");
}

include "addon/dbcon.php";

$user_id = $_SESSION['user']->id_user;
$query = "SELECT lr.*, u.user_name as response_by_name 
          FROM leave_requests lr 
          LEFT JOIN users_tb u ON lr.response_by = u.id_user 
          WHERE lr.user_id = ? 
          ORDER BY lr.created_at DESC";
$stmt = $con->prepare($query);
$stmt->bind_param("i", $user_id);
$stmt->execute();
$result = $stmt->get_result();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إجازاتي</title>
    <link rel="stylesheet" href="css/bootstrap.min.css">
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/all.min.css">
    <link rel="icon" href="css/icon.ico">
    <?php include "addon/topbar.php" ?>
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Cairo', sans-serif;
        }
        
        .leaves-container {
            max-width: 1200px;
            margin: 50px auto;
            padding: 20px;
        }
        
        .page-header {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            text-align: center;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        
        .page-title {
            color: #2c3e50;
            font-size: 28px;
            font-weight: bold;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .leave-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }
        
        .leave-card:hover {
            transform: translateY(-5px);
        }
        
        .leave-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            flex-wrap: wrap;
        }
        
        .employee-name {
            font-size: 20px;
            font-weight: bold;
            color: #2c3e50;
        }
        
        .status-badge {
            padding: 8px 15px;
            border-radius: 20px;
            font-weight: bold;
            font-size: 14px;
        }
        
        .status-pending {
            background: linear-gradient(45deg, #ffc107, #fd7e14);
            color: white;
        }
        
        .status-approved {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
        }
        
        .status-rejected {
            background: linear-gradient(45deg, #dc3545, #fd7e14);
            color: white;
        }
        
        .leave-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 15px;
        }
        
        .info-item {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #495057;
        }
        
        .info-item i {
            color: #667eea;
            width: 20px;
        }
        
        .leave-details {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 15px;
            border-right: 4px solid #667eea;
        }
        
        .admin-response {
            background: linear-gradient(45deg, #e3f2fd, #f3e5f5);
            padding: 15px;
            border-radius: 10px;
            border-right: 4px solid #667eea;
        }
        
        .response-header {
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        }
        
        .new-request-btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 15px 30px;
            border-radius: 50px;
            text-decoration: none;
            font-weight: bold;
            display: inline-block;
            margin-bottom: 20px;
            transition: all 0.3s ease;
        }
        
        .new-request-btn:hover {
            background: linear-gradient(45deg, #5a6fd8, #6a4190);
            transform: translateY(-2px);
            color: white;
            text-decoration: none;
        }
        
        .empty-state {
            text-align: center;
            padding: 50px;
            color: #6c757d;
        }
        
        .empty-state i {
            font-size: 64px;
            margin-bottom: 20px;
            color: #dee2e6;
        }
        
        .leave-type-badge {
            background: linear-gradient(45deg, #17a2b8, #6f42c1);
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .date-range {
            background: #e9ecef;
            padding: 10px 15px;
            border-radius: 10px;
            margin: 10px 0;
            text-align: center;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="leaves-container">
        <div class="page-header">
            <h1 class="page-title">
                <i class="fas fa-calendar-check"></i>
                إجازاتي
            </h1>
            <a href="request_leave.php" class="new-request-btn">
                <i class="fas fa-plus"></i>
                طلب إجازة جديدة
            </a>
        </div>
        
        <?php if($result->num_rows > 0): ?>
            <?php while($leave = $result->fetch_assoc()): ?>
                <div class="leave-card">
                    <div class="leave-header">
                        <div>
                            <h3 class="employee-name"><?php echo htmlspecialchars($leave['employee_name']); ?></h3>
                            <span class="leave-type-badge"><?php echo $leave['leave_type']; ?></span>
                        </div>
                        <span class="status-badge status-<?php 
                            echo $leave['status'] == 'قيد المراجعة' ? 'pending' : 
                                ($leave['status'] == 'موافق' ? 'approved' : 'rejected'); 
                        ?>">
                            <?php echo $leave['status']; ?>
                        </span>
                    </div>
                    
                    <div class="date-range">
                        <i class="fas fa-calendar"></i>
                        من <?php echo date('Y/m/d', strtotime($leave['start_date'])); ?> 
                        إلى <?php echo date('Y/m/d', strtotime($leave['end_date'])); ?>
                        (<?php echo $leave['days_count']; ?> يوم)
                    </div>
                    
                    <div class="leave-info">
                        <div class="info-item">
                            <i class="fas fa-calendar-plus"></i>
                            <span>تاريخ الطلب: <?php echo date('Y/m/d', strtotime($leave['request_date'])); ?></span>
                        </div>
                        <?php if($leave['response_date']): ?>
                            <div class="info-item">
                                <i class="fas fa-clock"></i>
                                <span>تاريخ الرد: <?php echo date('Y/m/d H:i', strtotime($leave['response_date'])); ?></span>
                            </div>
                        <?php endif; ?>
                    </div>
                    
                    <div class="leave-details">
                        <strong>تفاصيل الإجازة:</strong><br>
                        <?php echo nl2br(htmlspecialchars($leave['leave_details'])); ?>
                    </div>
                    
                    <?php if($leave['admin_response']): ?>
                        <div class="admin-response">
                            <div class="response-header">
                                <i class="fas fa-reply"></i>
                                رد الإدارة:
                                <?php if($leave['response_by_name']): ?>
                                    (بواسطة: <?php echo htmlspecialchars($leave['response_by_name']); ?>)
                                <?php endif; ?>
                            </div>
                            <?php echo nl2br(htmlspecialchars($leave['admin_response'])); ?>
                        </div>
                    <?php endif; ?>
                </div>
            <?php endwhile; ?>
        <?php else: ?>
            <div class="leave-card">
                <div class="empty-state">
                    <i class="fas fa-calendar-alt"></i>
                    <h3>لا توجد طلبات إجازة</h3>
                    <p>لم تقم بإرسال أي طلبات إجازة بعد</p>
                    <a href="request_leave.php" class="new-request-btn">
                        <i class="fas fa-plus"></i>
                        إرسال طلب إجازة
                    </a>
                </div>
            </div>
        <?php endif; ?>
    </div>
</body>
</html>
