<?php
session_start();
if (isset($_SESSION['user'])) {
  if ($_SESSION['user']->role === "Admin") {
  } else {
    header("location:../login.php", true);
    die("");
    echo "dont work";
  }
} else {
  header("location:../login.php", true);
  die("");
}

?>
<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>الحاسابات حسب التاريخ</title>
  <link rel="stylesheet" href="css/bootstrap.min.css">
  <link rel="stylesheet" href="css/styles.css">
  <link rel="stylesheet" href="css/all.min.css">
  <script src="js/all.min.js"></script>
  <link rel="icon" href="css/icon.ico">
  <script src="js/jquery.min.js"></script>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <?php include "addon/topbar.php" ?>


</head>

<body>
  <div class="search_ac">
    <select name="acout_type" id="selc2" placeholder='اختر مستخدم'>
      <option value="0">اختر صنف الحسابات</option>
      <option value="ايرادات">الايرادات</option>
      <option value="مصروفات">المصروفات</option>
      <option value="رواتب">الرواتب</option>

    </select>
    <input name="datee" id="date2" type="date" required>
    <label for=""> الى تاريخ</label>
    <input name="dates" id="date1" type="date" required>
    <label for="">اختر من تاريخ</label>
    <button class="btn btn-secondary"><a href="acounting2.php">اظهار حسب المستخدم</a></button>

  </div>


  <div class="wrapper2" id="tost_info">
    <div id="toast2">
      <div class="container-11">
        <i id="icon" class="fa-solid fa-money-bills "></i>
      </div>
      <div class="container-22">
        <p class="p1">CASH INFO </p>
        <p class="p2">تم اضافة البينانات</p>
      </div>
    </div>
  </div>
  <script>
    let x;
    let toast = document.getElementById("toast2");
         p1 = document.querySelector(".p1");
         p2 = document.querySelector(".p2");
    function TosatCash(ts,ic,tx1,ss,icC) {
      let  icon = document.getElementById("icon");
      cash = new Intl.NumberFormat('de-DE', {
        style: 'currency',
        currency: "IQD"
      }).format(ss);
      clearTimeout(x);
    icon.className=icC;
    toast.style.borderRight=ts;
    icon.style.color=ic;
    p1.innerText=tx1;
    p2.style.fontSize="20px";
    p2.innerText=cash;
    toast.style.transition='1s';
    toast.style.transform = "translateX(0)";
    toast.style.transition='1s';
    //  x = setTimeout(() => {
      //  toast.style.transform = "translateX(-500px)"
     // }, 30000);
    }
    
  </script>
 <!-- <script>
    function Nocoming() {

      clearTimeout(x);
      titlecash.innerText = "لاتوجد ايرادات لهذا التاريخ "
      icon.className = "fa-solid fa-money-bills";
      text.style.fontSize = "18px"
      text.innerText = "لاتوجد بيانات";
      toast.style.transition = '1s';
      toast.style.transform = "translateX(0)";
      toast.style.transition = '1s';
      x = setTimeout(() => {
        toast.style.transform = "translateX(-500px)"
      }, 10000);
    }
  </script>
  <script>
    let s;

    function ToastDepits(ss) {
      cash = new Intl.NumberFormat('de-DE', {
        style: 'currency',
        currency: "IQD"
      }).format(ss);
      clearTimeout(s);
      titlecash.innerText = "مجموع المصروفات "
      icon.className = "fa-solid fa-money-bills";
      text.style.fontSize = "18px"
      text.innerText = cash;
      toast.style.borderRight = "8px solid rgb(220 53 69)";
      icon.style.color = "red"
      toast.style.transition = '1s';
      toast.style.transform = "translateX(0)";
      toast.style.transition = '1s';
      s = setTimeout(() => {
        toast.style.transform = "translateX(-500px)"
      }, 10000);
    }
  </script>
    -->
  <script>
    let e;
    function reomvetoast() {
      e = setTimeout(() => {
        toast.style.transform = "translateX(-500px)"
      }, 100);
    }
  </script>

  <script>
    $("#selc2").change(function() {
      var selc = $(this).val();
      var date1 = $("#date1").change().val()
      var date2 = $("#date2").change().val()
      if (selc=="رواتب" && (date1 == "" || date1!=="") && (date2 == "" || date2!==""  )) {
        //console.log("بحث عن رواتب")
        $.ajax({
          method: "POST",
          url: "addon/acounting3.php",
          data: {
          myinput: selc,
          dates: date1,
          datee: date2,
          },
          success: function(data) {
            $("#divdata").html(data);
            $("#selc2").prop('selectedIndex', 0)
            //console.log(data)

          }
        });
 
      }else if(date1 !== "" && date2 !== "" && selc!=="رواتب"){
        $.ajax({
          method: "POST",
          url: "addon/acounting3.php",
          data: {
          myinput: selc,
          dates: date1,
          datee: date2,
          },
          success: function(data) {
            $("#divdata").html(data);
            $("#selc2").prop('selectedIndex', 0)
            //console.log(data)

          }
        });
        //console.log("all data checked !")
      }
    });
  </script>
  <div id="divdata">

  </div>
</body>

</html>