<?php
session_start();

try {
    // Check session and user role
    if (isset($_SESSION['user'])) {
        if ($_SESSION['user']->role !== "Admin") {
            header("location:http://localhost/roda/login.php", true);
            exit(); // Stop further execution
        }
    } else {
        header("location:http://localhost/roda/login.php", true);
        exit(); // Stop further execution
    }

    // Include database connection
    include "dbcon.php";

    // Check if the id parameter is set
    if (isset($_POST['id'])) {
        $search = $_POST['id'];

        // Perform the query
        $sql = "SELECT * FROM stud_tb, stud_pay, users_tb WHERE stud_pay.id_stud = stud_tb.id 
                AND stud_tb.userID = users_tb.id_user 
                AND users_tb.id_user = $search ORDER BY `stud_tb`.`datein` DESC";
        $result = mysqli_query($con, $sql);

        if (!$result) {
            throw new Exception("Query failed: " . mysqli_error($con)); // Catch query errors
        }

        if (mysqli_num_rows($result) > 0) {
            // متغيرات لحساب الإحصائيات
            $activeCount = 0;
            $soonCount = 0;
            $expiredCount = 0;
            $newRegCount = 0;
            $totalCount = 0;

            // بداية HTML للجدول
            $tableData = '';

            // Fetching the data
            while ($row = mysqli_fetch_assoc($result)) {
                // Extract data from the row
                $id = $row['id'];
                $id_note = $row['id_note'];
                $name = $row['name'];
                $age = $row['age'];
                $sex = $row['sex'];
                $catg = $row['catg'];
                $datein = $row['datein'];
                $p_phone = $row['p_phone'];
                $loc = $row['loc'];
                $date_exp = $row['date_exp'];
                $cash_stud = number_format($row['cash_stud']);
                $user_name = $row['user_name'];
                $id_pay = $row['id_pay'];

                $date_in = strtotime(date('Y-m-d'));
                $date_out = strtotime($date_exp);
                $stat = $date_out - $date_in;
                $cek = floor($stat / (60 * 60 * 24));

                // Determine the message based on remaining days
                if ($cek <= 0) {
                    $mes = '<h3 class="exp">منتهي</h3>';
                    $expiredCount++;
                } elseif ($cek <= 10 && $cek > 0) {
                    $mes = '<h3 class="soon">قريبا</h3>';
                    $soonCount++;
                } else {
                    $mes = '<h3 class="still">فعال</h3>';
                    $activeCount++;
                }

                // Check if the registration status is new or old
                $currentDate = new DateTime();
                $targetDate = new DateTime($datein);
                $regStatus='old';
                if ($currentDate->format('Y-m') == $targetDate->format('Y-m')) {
                    $regStatus = 'جديد';
                    $classStatus="still";
                    $newRegCount++;
                } else {
                    $regStatus = 'قديم';
                    $classStatus="soon";
                }

                // زيادة العداد الكلي
                $totalCount++;

                // إضافة البيانات إلى HTML
                $tableData .= '<tr id="tr_' . $id . '">
                    <td>
                        <button type="button" class="btn btn-secondary mb-1" id="edit_bnt" name="update">
                            <a style="text-decoration: none; color: whitesmoke;" href="editstud.php?id=' . $id . '">تعديل</a>
                        </button>
                        <button type="button" class="btn btn-secondary mb-1" id="reomve_btn" name="remove"
                                onclick="deletdata(' . $id . ')">حذف
                        </button>
                    </td>
                    <td>' . $user_name . '</td>
                    <td>' . $cek . '</td>
                    <td>' . $mes . '</td>
                    <td>IQD ' . $cash_stud . '</td>
                    <td>' . $date_exp . '</td>
                    <td>' . $datein . '</td>
                    <td>' . $p_phone . '</td>
                    <td>' . $catg . '</td>
                    <td>' . $loc . '</td>
                    <td>' . $sex . '</td>
                    <td>' . $age . '</td>
                    <td><p class="' . $classStatus . '">' . $regStatus . '</p></td>
                    <td>' . $name . '</td>
                    <td>' . $id_pay . '</td>
                </tr>';
            }

            // إعداد البيانات للإرجاع
            $statistics = array(
                'active' => $activeCount,
                'soon' => $soonCount,
                'expired' => $expiredCount,
                'newReg' => $newRegCount,
                'total' => $totalCount
            );

            // إرجاع البيانات كـ JSON
            header('Content-Type: application/json');
            echo json_encode(array(
                'tableData' => $tableData,
                'statistics' => $statistics
            ));

        } else {
            // في حالة عدم وجود بيانات
            $emptyData = '<td colspan="15" style="font-size: 25px;">لاتوجد بيانات طلاب لهذا المستخدم</td>';
            $emptyStats = array(
                'active' => 0,
                'soon' => 0,
                'expired' => 0,
                'newReg' => 0,
                'total' => 0
            );

            header('Content-Type: application/json');
            echo json_encode(array(
                'tableData' => $emptyData,
                'statistics' => $emptyStats
            ));
        }
    }
} catch (Exception $e) {
    // Catch any errors and display a message (for development purposes)
    echo "Error: " . $e->getMessage();
}
?>
