<?php
session_start();

// Include database connection
include "dbcon.php";

// Check if the id parameter is set
if (isset($_POST['id'])) {
    $search = $_POST['id'];

    // Perform the query
    $sql = "SELECT * FROM stud_tb, stud_pay, users_tb WHERE stud_pay.id_stud = stud_tb.id
            AND stud_tb.userID = users_tb.id_user
            AND users_tb.id_user = $search ORDER BY `stud_tb`.`datein` DESC";
    $result = mysqli_query($con, $sql);

    if (!$result) {
        echo "خطأ في الاستعلام: " . mysqli_error($con);
        exit;
    }

    if (mysqli_num_rows($result) > 0) {
        // متغيرات لحساب الإحصائيات
        $activeCount = 0;
        $soonCount = 0;
        $expiredCount = 0;
        $newRegCount = 0;
        $totalCount = 0;

        // Fetching the data
        while ($row = mysqli_fetch_assoc($result)) {
            // Extract data from the row
            $id = $row['id'];
            $id_note = $row['id_note'];
            $name = $row['name'];
            $age = $row['age'];
            $sex = $row['sex'];
            $catg = $row['catg'];
            $datein = $row['datein'];
            $p_phone = $row['p_phone'];
            $loc = $row['loc'];
            $date_exp = $row['date_exp'];
            $cash_stud = number_format($row['cash_stud']);
            $user_name = $row['user_name'];
            $id_pay = $row['id_pay'];

            $date_in = strtotime(date('Y-m-d'));
            $date_out = strtotime($date_exp);
            $stat = $date_out - $date_in;
            $cek = floor($stat / (60 * 60 * 24));

            // Determine the message based on remaining days
            if ($cek <= 0) {
                $mes = '<h3 class="exp">منتهي</h3>';
                $expiredCount++;
            } elseif ($cek <= 10 && $cek > 0) {
                $mes = '<h3 class="soon">قريبا</h3>';
                $soonCount++;
            } else {
                $mes = '<h3 class="still">فعال</h3>';
                $activeCount++;
            }

            // Check if the registration status is new or old
            $currentDate = new DateTime();
            $targetDate = new DateTime($datein);
            $regStatus='قديم';
            $classStatus="soon";
            if ($currentDate->format('Y-m') == $targetDate->format('Y-m')) {
                $regStatus = 'جديد';
                $classStatus="still";
                $newRegCount++;
            }

            // زيادة العداد الكلي
            $totalCount++;

            // عرض البيانات مباشرة
            ?>
            <tr id="tr_<?php echo $id ?>">
                <td>
                    <button type="button" class="btn btn-secondary mb-1" id="edit_bnt" name="update">
                        <a style="text-decoration: none; color: whitesmoke;" href="editstud.php?id=<?php echo $id ?>">تعديل</a>
                    </button>
                    <button type="button" class="btn btn-secondary mb-1" id="reomve_btn" name="remove"
                            onclick="deletdata(<?php echo $id ?>)">حذف
                    </button>
                </td>
                <td><?php echo $user_name ?></td>
                <td><?php echo $cek ?></td>
                <td><?php echo $mes ?></td>
                <td>IQD <?php echo $cash_stud ?></td>
                <td><?php echo $date_exp ?></td>
                <td><?php echo $datein ?></td>
                <td><?php echo $p_phone ?></td>
                <td><?php echo $catg ?></td>
                <td><?php echo $loc ?></td>
                <td><?php echo $sex ?></td>
                <td><?php echo $age ?></td>
                <td><p class="<?php echo $classStatus; ?>"><?php echo $regStatus ?></p></td>
                <td><?php echo $name ?></td>
                <td><?php echo $id_pay ?></td>
            </tr>
            <?php
        }

        // عرض الإحصائيات
        ?>
        <script>
            if (typeof updateStatistics === 'function') {
                updateStatistics({
                    active: <?php echo $activeCount; ?>,
                    soon: <?php echo $soonCount; ?>,
                    expired: <?php echo $expiredCount; ?>,
                    newReg: <?php echo $newRegCount; ?>,
                    total: <?php echo $totalCount; ?>
                });
            }
        </script>
        <?php

    } else {
        // في حالة عدم وجود بيانات
        echo '<tr><td colspan="15" style="font-size: 25px;">لاتوجد بيانات طلاب لهذا المستخدم</td></tr>';
        ?>
        <script>
            if (typeof hideStatistics === 'function') {
                hideStatistics();
            }
        </script>
        <?php
    }
}
?>
