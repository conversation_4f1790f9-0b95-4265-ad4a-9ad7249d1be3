<?php

session_start();
if(isset($_SESSION['user'])){
    if($_SESSION['user']->role==="Admin"){
        

 }else{
        header("location:http://localhost/roda/login.php",true);
        die("");
        echo "dont work";
    }
    }else{
        header("location:http://localhost/roda/login.php",true);
        die("");
}

include "dbcon.php";
if (isset($_GET['myinput'])) {
    if ($_GET['myinput'] == 'ايرادات') {

        $dates = $_GET['dates'];
        $datee = $_GET['datee'];
        $query = "SELECT * FROM stud_tb,stud_pay,users_tb WHERE  stud_pay.id_stud=stud_tb.id AND stud_tb.userID=users_tb.id_user AND DATE(stud_tb.datein) BETWEEN '$dates' AND '$datee'";
        $query_run = mysqli_query($con, $query);
        if (mysqli_num_rows($query_run) > 0) {


?>           <table class="table">
                <thead>
                    <tr>
                        <th scope="col"> مستخدم الحضانة </th>
                        <th scope="col">تاريخ التسجيل</th>
                        <th scope="col">قيمة الاشتراك</th>
                        <th scope="col">اسم الطالب</th>
                        <th scope="col">رقم الوصل </th>

                    </tr>
                </thead>
                <tbody>
                    <?php
                    $count = 0;
                    foreach ($query_run as $items) {


                        $count += $items['cash_stud'];

                    ?>

                        <td><?= $items['user_name']; ?></td>
                        <td><?= $items['datein']; ?></td>
                        <td> IQD <?= number_format($items['cash_stud']); ?></td>
                        <td><?= $items['name']; ?></td>
                        <td><?= $items['id_pay']; ?></td>

                </tbody>


            <?php


                    }
                    $msg1="مجموع الايرادات ";
                    $iconC="fa-solid fa-money-bills";
                    echo "<script> TosatCash(' 8px solid rgb(22, 204, 247)','rgb(22, 204, 247)','$msg1','$count','$iconC')</script>";
                } else {

                  echo  "<script>reomvetoast()</script>";
                    ?>
                     <table class="table">
                    <thead>
                        <tr>
                            <th > NO DATA FOUND</th>
                        </tr>
                    </thead>
                    <tbody>
                            <td style="font-size: 25px;">لاتوجد بيانات لهذا الفترة الزمنية</td>
                    </tbody>
                    <?php
                }
            }
            if ($_GET['myinput'] == 'مصروفات') {
                $dates = $_GET['dates'];
                $datee = $_GET['datee'];
                $query = "SELECT * FROM users_tb,depit_tb WHERE  depit_tb.userID=users_tb.id_user AND DATE(depit_tb.depit_date) BETWEEN '$dates' AND '$datee'";
                $query_run = mysqli_query($con, $query);
                if (mysqli_num_rows($query_run) > 0) {
            ?> <table class="table">
                <thead>
                    <tr>
                        <th scope="col"> مستخدم الحضانة </th>
                        <th scope="col"> التاريخ لعملية المصروفات</th>
                        <th scope="col">قيمة المصروفات</th>
                        <th scope="col"> وصف المصروفات </th>

                    </tr>
                </thead>
                <tbody>
                    <?php
                    $count2 = 0;
                    foreach ($query_run as $items) {
                        $count2 += $items['depit_cash'];
                    ?>

                        <td><?= $items['user_name']; ?></td>
                        <td><?= $items['depit_date']; ?></td>
                        <td> IQD <?= number_format($items['depit_cash']); ?></td>
                        <td><?= $items['depit_note']; ?></td>

                </tbody>


<?php
                    }
                    $msg1="مجموع المصروفات ";
                    $iconC="fa-solid fa-money-bills";
                    echo "<script> TosatCash(' 8px solid rgb(247, 22, 43)','rgb(247, 22, 43)','$msg1','$count2','$iconC')</script>";
                } else {

                    echo  "<script>reomvetoast()</script>";
                    ?>
                     <table class="table">
                    <thead>
                        <tr>
                            <th > NO DATA FOUND</th>
                        </tr>
                    </thead>
                    <tbody>
                            <td style="font-size: 25px;">لاتوجد بيانات لهذا الفترة الزمنية</td>
                    </tbody>
                    <?php
                }
            }
            if ($_GET['myinput'] == 'رواتب') {
                $dates = $_GET['dates'];
                $datee = $_GET['datee'];
                $query = "SELECT * FROM users_tb,employ_tb WHERE  employ_tb.userID=users_tb.id_user ";
                $query_run = mysqli_query($con, $query);
                if (mysqli_num_rows($query_run) > 0) {
            ?> <table class="table">
                <thead>
                    <tr>
                        <th scope="col"> مستخدم الحضانة </th>
                        <th scope="col"> الراتب الشهري </th>
                        <th scope="col">المسمى الوظيفي</th>
                        <th scope="col"> تاريخ المباشرة</th>
                        <th scope="col"> اسم الموظف</th>

                    </tr>
                </thead>
                <tbody>
                    <?php
                    $count3 = 0;
                    foreach ($query_run as $items) {
                        $count3 += $items['salary'];
                    ?>

                        <td><?= $items['user_name']; ?></td>
                        <td> IQD <?= number_format($items['salary']); ?></td>
                        <td><?= $items['job']; ?></td>
                        <td><?= $items['date_start']; ?></td>
                        <td><?= $items['f_name']; ?></td>

                </tbody>


<?php
                    }
                    $msg1="مجموع الرواتب ";
                    $iconC="fa-solid fa-people-group";
                    echo "<script> TosatCash(' 8px solid rgb(178, 102, 255)','rgb(178, 102, 255)','$msg1','$count3','$iconC')</script>";
                } else {

                    echo  "<script>reomvetoast()</script>";
                    ?>
                     <table class="table">
                    <thead>
                        <tr>
                            <th > NO DATA FOUND</th>
                        </tr>
                    </thead>
                    <tbody>
                            <td style="font-size: 25px;">لاتوجد بيانات  للموظفين</td>
                    </tbody>
                    <?php
                }
            }
        }


?>