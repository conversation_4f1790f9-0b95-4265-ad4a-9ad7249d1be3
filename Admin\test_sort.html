<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الترتيب</title>
    <link rel="stylesheet" href="css/bootstrap.min.css">
    <link rel="stylesheet" href="css/all.min.css">
    <style>
        .sortable-header {
            cursor: pointer;
            position: relative;
            user-select: none;
        }
        .sort-arrow {
            margin-left: 5px;
            font-size: 12px;
            color: #6c757d;
        }
        .sort-arrow.active {
            color: #007bff;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h2>اختبار وظيفة الترتيب</h2>
        
        <table class="table" id="studentsTable">
            <thead>
                <tr>
                    <th scope="col" class="sortable-header" onclick="sortTable('studentsTable', 0)">
                        مستخدم الحضانة
                        <span class="sort-arrow"><i class="fas fa-sort"></i></span>
                    </th>
                    <th scope="col" class="sortable-header" onclick="sortTable('studentsTable', 1)">
                        تاريخ التسجيل
                        <span class="sort-arrow"><i class="fas fa-sort"></i></span>
                    </th>
                    <th scope="col" class="sortable-header" onclick="sortTable('studentsTable', 2)">
                        قيمة الاشتراك
                        <span class="sort-arrow"><i class="fas fa-sort"></i></span>
                    </th>
                    <th scope="col" class="sortable-header" onclick="sortTable('studentsTable', 3)">
                        اسم الطالب
                        <span class="sort-arrow"><i class="fas fa-sort"></i></span>
                    </th>
                    <th scope="col" class="sortable-header" onclick="sortTable('studentsTable', 4)">
                        رقم الوصل
                        <span class="sort-arrow"><i class="fas fa-sort"></i></span>
                    </th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>أحمد محمد</td>
                    <td>2024-01-15</td>
                    <td>50,000</td>
                    <td>علي أحمد</td>
                    <td>1001</td>
                </tr>
                <tr>
                    <td>فاطمة علي</td>
                    <td>2024-01-10</td>
                    <td>75,000</td>
                    <td>سارة محمد</td>
                    <td>1002</td>
                </tr>
                <tr>
                    <td>محمد حسن</td>
                    <td>2024-01-20</td>
                    <td>30,000</td>
                    <td>يوسف علي</td>
                    <td>1003</td>
                </tr>
                <tr>
                    <td>زينب أحمد</td>
                    <td>2024-01-05</td>
                    <td>100,000</td>
                    <td>مريم حسن</td>
                    <td>1004</td>
                </tr>
            </tbody>
        </table>
    </div>

    <script>
    function sortTable(tableId, columnIndex) {
        console.log('Sorting table:', tableId, 'column:', columnIndex);
        
        const table = document.getElementById(tableId);
        if (!table) {
            console.log('Table not found:', tableId);
            return;
        }
        
        const tbody = table.querySelector('tbody');
        if (!tbody) {
            console.log('Tbody not found in table:', tableId);
            return;
        }
        
        const rows = Array.from(tbody.querySelectorAll('tr'));
        if (rows.length === 0) {
            console.log('No rows found in table:', tableId);
            return;
        }
        
        const headers = table.querySelectorAll('th');
        if (columnIndex >= headers.length) {
            console.log('Column index out of range:', columnIndex);
            return;
        }
        
        const header = headers[columnIndex];
        const sortArrow = header.querySelector('.sort-arrow i');
        
        if (!sortArrow) {
            console.log('Sort arrow not found in header:', columnIndex);
            return;
        }
        
        // Reset all other arrows in this table
        table.querySelectorAll('.sort-arrow i').forEach(arrow => {
            if (arrow !== sortArrow) {
                arrow.className = 'fas fa-sort';
                arrow.parentElement.classList.remove('active');
            }
        });
        
        let isAscending = true;
        
        // Check current sort direction
        if (sortArrow.classList.contains('fa-sort-up')) {
            isAscending = false;
            sortArrow.className = 'fas fa-sort-down';
        } else {
            isAscending = true;
            sortArrow.className = 'fas fa-sort-up';
        }
        
        sortArrow.parentElement.classList.add('active');
        
        // Sort rows
        rows.sort((a, b) => {
            if (!a.cells[columnIndex] || !b.cells[columnIndex]) {
                return 0;
            }
            
            const aText = a.cells[columnIndex].textContent.trim();
            const bText = b.cells[columnIndex].textContent.trim();
            
            console.log('Comparing:', aText, 'vs', bText);
            
            // Check if it's a number (for amounts and IDs)
            const aNum = parseFloat(aText.replace(/[^\d.-]/g, ''));
            const bNum = parseFloat(bText.replace(/[^\d.-]/g, ''));
            
            let comparison = 0;
            
            if (!isNaN(aNum) && !isNaN(bNum) && aText.match(/\d/)) {
                // Numeric comparison
                comparison = aNum - bNum;
                console.log('Numeric comparison:', aNum, 'vs', bNum, '=', comparison);
            } else {
                // Text comparison
                comparison = aText.localeCompare(bText, 'ar', {numeric: true});
                console.log('Text comparison:', aText, 'vs', bText, '=', comparison);
            }
            
            return isAscending ? comparison : -comparison;
        });
        
        // Clear tbody and append sorted rows
        tbody.innerHTML = '';
        rows.forEach(row => tbody.appendChild(row));
        
        console.log('Sorting completed for table:', tableId);
    }
    </script>
</body>
</html>
