# تفعيل محرك إعادة الكتابة
RewriteEngine On

# توجيه الصفحة الرئيسية إلى index.php
DirectoryIndex index.php

# منع الوصول إلى ملفات النسخ الاحتياطية
<Files "*.sql">
    Order allow,deny
    Deny from all
</Files>

# منع الوصول إلى ملفات الإعدادات الحساسة
<Files "db_connect.php">
    Order allow,deny
    Deny from all
</Files>

# تحسين الأمان - منع عرض محتويات المجلدات
Options -Indexes

# ضغط الملفات لتحسين الأداء
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# تحديد انتهاء صلاحية الملفات الثابتة
<IfModule mod_expires.c>
    ExpiresActive on
    ExpiresByType text/css "access plus 1 year"
    ExpiresByType application/javascript "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/ico "access plus 1 year"
</IfModule>
