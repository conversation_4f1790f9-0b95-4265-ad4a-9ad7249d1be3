<?php
session_start();
if(isset($_SESSION['user'])){
    if($_SESSION['user']->role==="Mod"){
        

 }else{
        header("location:../login.php",true);
        die("");
        echo "dont work";
    }
    }else{
        header("location:../login.php",true);
        die("");
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>حضور الطلاب </title>
    <link rel="stylesheet" href="css/bootstrap.min.css">
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/all.min.css">
    <script src="js/all.min.js"></script>
    <link rel="icon" href="css/icon.ico">
    <script src="js/jquery.min.js"></script>
    <script src="js/jquery.dataTables.min.js"></script>
    <link rel="stylesheet" href="css/jquery.dataTables.min.css">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <?php include "addon/topbar.php" ?>
      <?php include "addon/dbcon.php" ?>
      
      <style>
        /* تنسيق الـ tooltip لحالة الاشتراك */
        .subscription-tooltip {
          position: relative;
          cursor: pointer;
        }
        
        .subscription-tooltip .tooltiptext {
          visibility: hidden;
          width: 200px;
          background-color: #333;
          color: #fff;
          text-align: center;
          border-radius: 6px;
          padding: 8px;
          position: absolute;
          z-index: 1;
          bottom: 125%;
          left: 50%;
          margin-left: -100px;
          opacity: 0;
          transition: opacity 0.3s;
          font-size: 12px;
        }
        
        .subscription-tooltip .tooltiptext::after {
          content: "";
          position: absolute;
          top: 100%;
          left: 50%;
          margin-left: -5px;
          border-width: 5px;
          border-style: solid;
          border-color: #333 transparent transparent transparent;
        }
        
        .subscription-tooltip:hover .tooltiptext {
          visibility: visible;
          opacity: 1;
        }
        
        .search_ac {
          margin-bottom: 20px;
          padding: 15px;
          background-color: #f8f9fa;
          border-radius: 8px;
        }
        
        .search_ac label {
          color: #666;
          font-weight: bold;
          margin-left: 10px;
        }
        
        /* تنسيق حالة الحضور */
        .attendance-status {
          font-weight: bold;
          padding: 4px 8px;
          border-radius: 4px;
          text-align: center;
        }
        
        .attendance-present {
          color: #28a745;
          background-color: #d4edda;
        }
        
        .attendance-absent {
          color: #dc3545;
          background-color: #f8d7da;
        }
      </style>
   </head>
   <body>
   <form action="" method="POST">
  <div class="search_ac">
  <select name="acout_type" id="selc2" placeholder='اختر مستخدم'>
      <option value="0" selected disabled>اختر مستخدم </option>
     <?php
            $sql="SELECT * FROM users_tb ";
            $result=mysqli_query($con,$sql);
            if($result){
                while($row=mysqli_fetch_assoc($result)){
                    $user_name=$row['user_name'];
                    $id=$row['id_user'];
                    // الحفاظ على القيمة المختارة
                    $selected = (isset($_POST['acout_type']) && $_POST['acout_type'] == $id) ? 'selected' : '';
                echo '<option value='.$id.' '.$selected.'>'.$user_name.'</option>';
                }
            }
            ?>
     
     </select>
   
    <input name="dateChose" type="date" value="<?php echo isset($_POST['dateChose']) ? $_POST['dateChose'] : ''; ?>" required>
    <!-- <label for=""> اختر تاريخ الحضور المراد عرضه</label> -->
    <button class="btn btn-warning" name='myInput'  > اظهار</button> 
  </div>
   </form>
   <section class="button_excel" id="btn_ex">
    <button type="submit" class="bt_execl" name="excl" onclick="exportex()">تحميل أكسل</button>
    <button type="button" class="bt_print" onclick="printTable()">طباعة</button>
   </section>
  <div class="wrapper2" id="tost_info">
    <div id="toast2">
      <div class="container-11">
        <i id="icon" class="fa-solid fa-money-bills "></i>
      </div>
      <div class="container-22">
        <p class="p1">CASH INFO </p>
        <p class="p2">تم اضافة البينانات</p>
      </div>
    </div>
  </div>
  <table class="table" id="Table">
                            <thead>
                            <tr>
                            
                            <td>تاريخ الحضور</td>
                            <td>حالة الحضور</td>
                            <td>حالة الاشتراك</td>
                            <td>صنف الدراسة</td>
                            <td> اسم المستخدم</td>
                            <td>اسم الطالب</td>
                            </tr>
                            </thead>
                            <tbody>
   
   <script>
   function exportex(){
const tdElement = document.getElementsByClassName('user')[1];
const useratt = tdElement ? tdElement.id : null;
const tdElement2 = document.getElementsByClassName('date')[1];
const dateatt = tdElement2 ? tdElement2.id : null;
console.log(useratt,dateatt)
$.ajax({
  method: "post",
  url: "addon/export",
  data: {
    useratt:useratt,
    dateatt:dateatt,
  },
  success: function (data) {
    window.location.href = "addon/export?useratt=" + useratt + "&dateatt=" + dateatt;
  }
});
    }

    function printTable(){
        // إنشاء نافذة جديدة للطباعة
        var printWindow = window.open('', '_blank');

        // نسخ الجدول وإزالة عمود العمليات
        var table = document.getElementById('Table').cloneNode(true);

        // إزالة عمود العمليات من الرأس والصفوف
        var rows = table.querySelectorAll('tr');
        rows.forEach(function(row) {
            var cells = row.querySelectorAll('th, td');
            // إزالة العمود الأول (العمليات) إذا وجد
            if (cells.length > 0 && (cells[0].textContent.includes('العمليات') || cells[0].querySelector('button') || cells[0].querySelector('a'))) {
                cells[0].remove();
            }
        });

        var tableContent = table.outerHTML;

        // إنشاء محتوى HTML للطباعة
        var printContent = `
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <title>تقرير حضور الطلاب</title>
            <style>
                @font-face {
                    font-family:"LamaSans-Medium";
                    src: url(css/JannaLT.ttf);
                }

                body {
                    font-family: "LamaSans-Medium", Arial, sans-serif;
                    direction: rtl;
                    margin: 20px;
                    background: white;
                }

                .print-header {
                    text-align: center;
                    margin-bottom: 30px;
                    border-bottom: 2px solid #333;
                    padding-bottom: 20px;
                }

                .print-header h1 {
                    color: #333;
                    margin-bottom: 10px;
                    font-size: 24px;
                }

                .print-header p {
                    color: #666;
                    margin: 5px 0;
                    font-size: 14px;
                }

                table {
                    width: 100%;
                    border-collapse: collapse;
                    margin: 20px 0;
                    font-size: 12px;
                }

                table th, table td {
                    border: 1px solid #333;
                    padding: 8px;
                    text-align: center;
                }

                table thead {
                    background-color: #f8f9fa;
                    font-weight: bold;
                }

                table thead th {
                    background-color: #333;
                    color: white;
                    font-weight: bold;
                }

                .attendance-present {
                    color: green;
                    font-weight: bold;
                }

                .attendance-absent {
                    color: red;
                    font-weight: bold;
                }

                .print-footer {
                    margin-top: 30px;
                    text-align: center;
                    font-size: 12px;
                    color: #666;
                    border-top: 1px solid #ccc;
                    padding-top: 10px;
                }

                @media print {
                    body { margin: 0; }
                    .print-header { page-break-inside: avoid; }
                    table { page-break-inside: auto; }
                    tr { page-break-inside: avoid; page-break-after: auto; }
                }
            </style>
        </head>
        <body>
            <div class="print-header">
                <h1>تقرير حضور الطلاب</h1>
                <p>تاريخ التقرير: ${new Date().toLocaleDateString('ar-EG')}</p>
                <p>وقت الطباعة: ${new Date().toLocaleTimeString('ar-EG')}</p>
            </div>

            ${tableContent}

            <div class="print-footer">
                <p>تم إنشاء هذا التقرير بواسطة نظام إدارة الروضة</p>
            </div>
        </body>
        </html>
        `;

        // كتابة المحتوى في النافذة الجديدة
        printWindow.document.write(printContent);
        printWindow.document.close();

        // انتظار تحميل المحتوى ثم طباعة
        printWindow.onload = function() {
            printWindow.print();
            printWindow.close();
        };
    }

    function secRun(){
    let sec =document.querySelector(".button_excel");
    sec.style.visibility="visible";
    }
    let x;
    let toast = document.getElementById("toast2");
         p1 = document.querySelector(".p1");
         p2 = document.querySelector(".p2");
    function Warning(ts,ic,tx1,ss,icC,time) {
      clearTimeout(x);
    icon.className=icC;
    toast.style.borderRight=ts;
    icon.style.color=ic;
    p1.innerText=tx1;
    p2.style.fontSize="16px";
    p2.innerText=ss;
    toast.style.transition='1s';
    toast.style.transform = "translateX(0)";
    toast.style.transition='1s';
      x = setTimeout(() => {
        toast.style.transform = "translateX(-500px)"
      }, time);
    }
    
  </script>
   <?php
    if(isset($_POST['myInput'])){
        if(!isset($_POST['acout_type']) || $_POST['acout_type'] == '0'){
        $msg1=" ! انتبه ";
        $msg2="يرجى  اختيار جميع  الحقول  ";
        $iconC="fa-solid fa-circle-info";
        $time=5000;
        echo "<script> Warning('8px solid rgb(247, 167, 22)','#f7a716','$msg1','$msg2','$iconC','$time')</script>";
        }else{
            $user=$_POST['acout_type'];
            $date=$_POST['dateChose'];
            
            // استعلام محسن مع جلب بيانات الاشتراك من جدول stud_pay وحالة الحضور
            $sql = "SELECT users_tb.*, stud_tb.*, stat.*, stud_pay.date_exp, stud_pay.cash_stud
                   FROM users_tb
                   INNER JOIN stud_tb ON users_tb.id_user = stud_tb.userID
                   INNER JOIN stat ON stud_tb.id = stat.id_stud
                   LEFT JOIN stud_pay ON stud_tb.id = stud_pay.id_stud
                   WHERE users_tb.id_user = '$user'
                   AND stat.data_stat = '$date'";
            
            $resel=mysqli_query($con,$sql);
            if(mysqli_num_rows($resel)>0){
                $count=mysqli_num_rows($resel);
                $msg1=" حضور ";
                $msg2=" $count : مجموع  حضور الطلاب   ";
                $iconC="fa-solid fa-circle-info";
                $time=50000;
                echo "<script> Warning('8px solid rgb(34, 196, 242)','rgb(34, 196, 242)','$msg1','$msg2','$iconC','$time')</script>";
                    ?>
<script>secRun()</script>
                    <?php
                   

                while($row=mysqli_fetch_assoc($resel)){
                    ?>
                    
                     <tr>
                     <td class="date" id="<?php echo $row['data_stat']    ?>"><?php echo $row['data_stat']    ?></td>
                        
                        <!-- عمود حالة الحضور الجديد -->
                        <td>
                          <?php 
                          $attendance_status = $row['stat_stud'] ?? 'غير محدد';
                          $status_class = '';
                          
                          if($attendance_status == 'حاضر') {
                              $status_class = 'attendance-present';
                          } elseif($attendance_status == 'غايب') {
                              $status_class = 'attendance-absent';
                          }
                          
                          echo '<span class="attendance-status ' . $status_class . '">' . $attendance_status . '</span>';
                          ?>
                        </td>
                        
                        <td>
                          <?php 
                          // تحديد حالة الاشتراك بناءً على تاريخ الانتهاء
                          $subscription_status = 'غير محدد';
                          $subscription_color = 'gray';
                          $tooltip_info = '';
                          
                          if(isset($row['date_exp']) && !empty($row['date_exp'])) {
                              $expiry_date = new DateTime($row['date_exp']);
                              $current_date = new DateTime();
                              
                              if($expiry_date > $current_date) {
                                  $subscription_status = 'نشط';
                                  $subscription_color = 'green';
                                  $days_remaining = $current_date->diff($expiry_date)->days;
                                  $tooltip_info = 'ينتهي في: ' . $row['date_exp'] . '<br>متبقي: ' . $days_remaining . ' يوم';
                              } else {
                                  $subscription_status = 'منتهي';
                                  $subscription_color = 'red';
                                  $days_expired = $expiry_date->diff($current_date)->days;
                                  $tooltip_info = 'انتهى في: ' . $row['date_exp'] . '<br>منتهي منذ: ' . $days_expired . ' يوم';
                              }
                              
                              // إضافة معلومات المبلغ المدفوع إذا كانت متوفرة
                              if(isset($row['cash_stud']) && !empty($row['cash_stud'])) {
                                  $tooltip_info .= '<br>المبلغ المدفوع: ' . $row['cash_stud'] . ' جنيه';
                              }
                          }
                          
                          // عرض حالة الاشتراك مع tooltip
                          if($tooltip_info) {
                              echo '<div class="subscription-tooltip">
                                      <p style="color: ' . $subscription_color . '; font-weight: bold;">' . $subscription_status . '</p>
                                      <span class="tooltiptext">' . $tooltip_info . '</span>
                                    </div>';
                          } else {
                              echo '<p style="color: ' . $subscription_color . '; font-weight: bold;">' . $subscription_status . '</p>';
                          }
                          ?>
                        </td>
                        <td><?php echo $row['catg']    ?></td>
                        <td class="user" id="<?php echo $row['id_user']    ?>"><?php echo $row['user_name']    ?></td>
                        <td><?php echo $row['name']    ?></td>
                     </tr>


                    <?php 
                }
                
                ?>
                </tbody>
                </table>
                <?php
            }else{
                $msg1="! لايوجد ";
                $msg2="لاتوجد بيانات لهذا التاريخ ";
                $iconC="fa-solid fa-circle-info";
                $time=5000;
                echo "<script> Warning('8px solid rgb(243, 35, 82)','rgb(243, 35, 82)','$msg1','$msg2','$iconC','$time')</script>";

            }
           
        
        }
    }
  ?>
  <script>
  $(document).ready(function() {
    // التأكد من وجود بيانات حقيقية قبل تطبيق DataTable
    if($("#Table tbody tr").length > 0 && !$("#Table tbody tr td").first().attr('colspan')) {
        $("#Table").DataTable({
            "language": {
                "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Arabic.json"
            },
            "order": [[ 1, "desc" ]], // ترتيب حسب التاريخ (الأحدث أولاً)
            "columnDefs": [
                { "orderable": true, "targets": "_all" }
            ]
        });
    }
});
</script>
</body>
</html>