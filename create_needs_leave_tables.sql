-- إن<PERSON><PERSON>ء جدول طلبات الاحتياجات
CREATE TABLE IF NOT EXISTS `needs_requests` (
  `id_need` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `need_name` varchar(255) NOT NULL,
  `need_type` enum('احتياج','صيانة') NOT NULL,
  `need_details` text NOT NULL,
  `request_date` date NOT NULL,
  `status` enum('قيد المراجعة','تم التوفير','لم يتوفر') NOT NULL DEFAULT 'قيد المراجعة',
  `admin_response` text DEFAULT NULL,
  `response_by` int(11) DEFAULT NULL,
  `response_date` datetime DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id_need`),
  KEY `user_id` (`user_id`),
  KEY `response_by` (`response_by`),
  KEY `status` (`status`),
  KEY `request_date` (`request_date`),
  CONSTRAINT `needs_requests_user_fk` FOREIGN KEY (`user_id`) REFERENCES `users_tb` (`id_user`) ON DELETE CASCADE,
  CONSTRAINT `needs_requests_response_fk` FOREIGN KEY (`response_by`) REFERENCES `users_tb` (`id_user`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إنشاء جدول طلبات الإجازة
CREATE TABLE IF NOT EXISTS `leave_requests` (
  `id_leave` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `employee_name` varchar(255) NOT NULL,
  `start_date` date NOT NULL,
  `end_date` date NOT NULL,
  `days_count` int(11) NOT NULL,
  `leave_type` enum('مرضية','عرضية','طارئة','زمنية','ظروف أخرى') NOT NULL,
  `leave_details` text NOT NULL,
  `request_date` date NOT NULL,
  `status` enum('قيد المراجعة','موافق','غير موافق') NOT NULL DEFAULT 'قيد المراجعة',
  `admin_response` text DEFAULT NULL,
  `response_by` int(11) DEFAULT NULL,
  `response_date` datetime DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id_leave`),
  KEY `user_id` (`user_id`),
  KEY `response_by` (`response_by`),
  KEY `status` (`status`),
  KEY `request_date` (`request_date`),
  KEY `start_date` (`start_date`),
  KEY `end_date` (`end_date`),
  CONSTRAINT `leave_requests_user_fk` FOREIGN KEY (`user_id`) REFERENCES `users_tb` (`id_user`) ON DELETE CASCADE,
  CONSTRAINT `leave_requests_response_fk` FOREIGN KEY (`response_by`) REFERENCES `users_tb` (`id_user`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- إضافة فهارس إضافية لتحسين الأداء
CREATE INDEX idx_needs_user_status ON needs_requests(user_id, status);
CREATE INDEX idx_needs_request_date ON needs_requests(request_date DESC);
CREATE INDEX idx_leave_user_status ON leave_requests(user_id, status);
CREATE INDEX idx_leave_request_date ON leave_requests(request_date DESC);
CREATE INDEX idx_leave_date_range ON leave_requests(start_date, end_date);

-- إدراج بيانات تجريبية (اختيارية)
-- يمكن حذف هذا القسم إذا لم تكن تريد بيانات تجريبية

-- INSERT INTO needs_requests (user_id, need_name, need_type, need_details, request_date) VALUES
-- (1, 'أقلام ملونة', 'احتياج', 'نحتاج إلى أقلام ملونة للأطفال في الصف', CURDATE()),
-- (1, 'إصلاح المكيف', 'صيانة', 'المكيف في الصف الأول لا يعمل بشكل جيد', CURDATE());

-- INSERT INTO leave_requests (user_id, employee_name, start_date, end_date, days_count, leave_type, leave_details, request_date) VALUES
-- (1, 'أحمد محمد', '2024-01-15', '2024-01-17', 3, 'مرضية', 'إجازة مرضية لظروف صحية', CURDATE()),
-- (1, 'فاطمة علي', '2024-01-20', '2024-01-22', 3, 'عرضية', 'إجازة عرضية لظروف شخصية', CURDATE());
